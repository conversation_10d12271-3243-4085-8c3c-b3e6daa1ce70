'use client'

import { createContext, useContext, useEffect, useState } from 'react'
import { getUserPreferences, saveUserPreferences } from '@/lib/database'

type Theme = 'light' | 'dark'

interface ThemeContextType {
  theme: Theme
  toggleTheme: () => void
  loadUserTheme: () => Promise<void>
  saveThemeToDatabase: (theme: Theme) => Promise<void>
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [theme, setTheme] = useState<Theme>('light')
  const [isInitialized, setIsInitialized] = useState(false)

  const toggleTheme = () => {
    setTheme(prevTheme => prevTheme === 'light' ? 'dark' : 'light')
  }

  const loadUserTheme = async () => {
    try {
      // Try to get user preferences from database
      const userPrefs = await getUserPreferences()

      if (userPrefs && userPrefs.theme) {
        setTheme(userPrefs.theme)
      }
    } catch (error) {
      console.error('Error loading user theme preference:', error)
    }
  }

  const saveThemeToDatabase = async (themeToSave: Theme) => {
    try {
      await saveUserPreferences(themeToSave)
    } catch (error) {
      console.error('Error saving theme preference to database:', error)
    }
  }

  // Load initial theme preference from localStorage
  useEffect(() => {
    const savedTheme = localStorage.getItem('theme') as Theme
    if (savedTheme) {
      setTheme(savedTheme)
    } else if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
      setTheme('dark')
    }
    setIsInitialized(true)
  }, [])

  // Apply theme to document and save to localStorage
  useEffect(() => {
    if (!isInitialized) return

    // Apply theme to document
    const root = window.document.documentElement
    root.classList.remove('light', 'dark')
    root.classList.add(theme)

    // Always save to localStorage as fallback
    localStorage.setItem('theme', theme)
  }, [theme, isInitialized])

  return (
    <ThemeContext.Provider value={{ theme, toggleTheme, loadUserTheme, saveThemeToDatabase }}>
      {children}
    </ThemeContext.Provider>
  )
}

export function useTheme() {
  const context = useContext(ThemeContext)
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  return context
}
