'use client'

import { Check } from 'lucide-react'

interface PasswordRequirementsProps {
  password: string
  className?: string
}

interface RequirementItemProps {
  text: string
  isValid: boolean
}

function RequirementItem({ text, isValid }: RequirementItemProps) {
  return (
    <li className={`flex items-center space-x-2 transition-colors duration-200 ${
      isValid ? 'text-green-700 dark:text-green-300' : 'text-blue-700 dark:text-blue-300'
    }`}>
      <div className={`flex-shrink-0 w-4 h-4 rounded-full flex items-center justify-center transition-all duration-200 ${
        isValid 
          ? 'bg-green-100 dark:bg-green-900/30 border border-green-300 dark:border-green-700' 
          : 'bg-blue-100 dark:bg-blue-900/30 border border-blue-300 dark:border-blue-700'
      }`}>
        {isValid ? (
          <Check className="w-3 h-3 text-green-600 dark:text-green-400" />
        ) : (
          <div className={`w-1.5 h-1.5 rounded-full ${
            isValid ? 'bg-green-600 dark:bg-green-400' : 'bg-blue-600 dark:bg-blue-400'
          }`} />
        )}
      </div>
      <span className="text-xs">{text}</span>
    </li>
  )
}

export default function PasswordRequirements({ password, className = '' }: PasswordRequirementsProps) {
  // Password validation functions
  const hasMinLength = password.length >= 12
  const hasUppercase = /[A-Z]/.test(password)
  const hasLowercase = /[a-z]/.test(password)
  const hasNumber = /\d/.test(password)
  const hasSpecialChar = /[@$!%*?&]/.test(password)

  const requirements = [
    { text: 'At least 12 characters long', isValid: hasMinLength },
    { text: 'One uppercase letter (A-Z)', isValid: hasUppercase },
    { text: 'One lowercase letter (a-z)', isValid: hasLowercase },
    { text: 'One number (0-9)', isValid: hasNumber },
    { text: 'One special character (@$!%*?&)', isValid: hasSpecialChar },
  ]

  const allValid = requirements.every(req => req.isValid)

  return (
    <div className={`bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md p-3 transition-all duration-300 ${
      allValid ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800' : ''
    } ${className}`}>
      <p className={`text-xs font-medium mb-2 transition-colors duration-200 ${
        allValid ? 'text-green-800 dark:text-green-200' : 'text-blue-800 dark:text-blue-200'
      }`}>
        Password Requirements:
      </p>
      <ul className="space-y-1.5">
        {requirements.map((requirement, index) => (
          <RequirementItem
            key={index}
            text={requirement.text}
            isValid={requirement.isValid}
          />
        ))}
      </ul>
      {allValid && (
        <div className="mt-2 pt-2 border-t border-green-200 dark:border-green-800">
          <div className="flex items-center space-x-2 text-green-700 dark:text-green-300">
            <Check className="w-4 h-4" />
            <span className="text-xs font-medium">All requirements met!</span>
          </div>
        </div>
      )}
    </div>
  )
}
