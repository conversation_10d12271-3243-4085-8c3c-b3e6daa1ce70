-- Migration to add Maximum Favorable Excursion (MFE) field to trades table
-- This migration adds the mfe_price column for stock trades

-- Add the MFE price column to the trades table
ALTER TABLE trades 
ADD COLUMN mfe_price DECIMAL(10,2);

-- Add comment to document the new column
COMMENT ON COLUMN trades.mfe_price IS 'Maximum Favorable Excursion price for stock trades - the highest favorable price reached during the trade';

-- Update any existing trades to have NULL mfe_price (which is the default)
-- No data update needed since new column defaults to NULL

-- Verify the column was added successfully
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'trades' AND column_name = 'mfe_price';
