-- Migration to fix existing stock trades by removing fees and recalculating profit/loss
-- Run this in your Supabase SQL Editor AFTER updating the trigger function

-- Step 1: Update the trigger function first (should already be done via schema.sql update)
CREATE OR REPLACE FUNCTION calculate_profit_loss()
RETURNS TRIGGER AS $$
BEGIN
    -- Calculate fees based on trade type
    IF NEW.trade_type = 'option' THEN
        -- Options: $1.30 per contract
        NEW.fees = NEW.contracts * 1.30;
    ELSE
        -- Stocks: No fees
        NEW.fees = 0;
    END IF;

    -- Calculate profit/loss when both entry and exit prices are available
    IF NEW.exit_price IS NOT NULL AND NEW.entry_price IS NOT NULL THEN
        IF NEW.trade_type = 'option' THEN
            -- Options: multiply by 100 for contract multiplier
            NEW.profit_loss = (NEW.exit_price - NEW.entry_price) * NEW.contracts * 100 - NEW.fees;
        ELSE
            -- Stocks: calculate based on direction (no fees)
            IF NEW.direction = 'long' THEN
                NEW.profit_loss = (NEW.exit_price - NEW.entry_price) * NEW.shares;
            ELSE -- short
                NEW.profit_loss = (NEW.entry_price - NEW.exit_price) * NEW.shares;
            END IF;
        END IF;
    END IF;

    -- Calculate R-return for stock trades (risk-reward ratio)
    IF NEW.trade_type = 'stock' AND NEW.stop_price IS NOT NULL AND NEW.entry_price IS NOT NULL THEN
        DECLARE
            risk_amount DECIMAL(10,2);
            reward_amount DECIMAL(10,2);
        BEGIN
            IF NEW.direction = 'long' THEN
                -- Long: risk is entry - stop, reward is exit - entry
                risk_amount = (NEW.entry_price - NEW.stop_price) * NEW.shares;
                IF NEW.exit_price IS NOT NULL THEN
                    reward_amount = (NEW.exit_price - NEW.entry_price) * NEW.shares;
                    IF risk_amount > 0 THEN
                        NEW.r_return = reward_amount / risk_amount;
                    END IF;
                END IF;
            ELSE -- short
                -- Short: risk is stop - entry, reward is entry - exit
                risk_amount = (NEW.stop_price - NEW.entry_price) * NEW.shares;
                IF NEW.exit_price IS NOT NULL THEN
                    reward_amount = (NEW.entry_price - NEW.exit_price) * NEW.shares;
                    IF risk_amount > 0 THEN
                        NEW.r_return = reward_amount / risk_amount;
                    END IF;
                END IF;
            END IF;
        END;
    END IF;

    -- Update the updated_at timestamp
    NEW.updated_at = NOW();

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Step 2: Show current stock trades with fees (for verification)
SELECT 
    id,
    ticker,
    trade_type,
    direction,
    shares,
    entry_price,
    exit_price,
    fees,
    profit_loss,
    entry_datetime
FROM trades 
WHERE trade_type = 'stock' AND fees > 0
ORDER BY entry_datetime DESC;

-- Step 3: Fix existing stock trades by setting fees to 0 and recalculating profit/loss
UPDATE trades 
SET 
    fees = 0,
    profit_loss = CASE 
        WHEN exit_price IS NOT NULL AND entry_price IS NOT NULL THEN
            CASE 
                WHEN direction = 'long' THEN (exit_price - entry_price) * shares
                ELSE (entry_price - exit_price) * shares
            END
        ELSE profit_loss
    END,
    updated_at = NOW()
WHERE trade_type = 'stock';

-- Step 4: Show updated stock trades (for verification)
SELECT 
    id,
    ticker,
    trade_type,
    direction,
    shares,
    entry_price,
    exit_price,
    fees,
    profit_loss,
    entry_datetime
FROM trades 
WHERE trade_type = 'stock'
ORDER BY entry_datetime DESC;

-- Step 5: Show summary of changes
SELECT 
    'Stock Trades Fixed' as status,
    COUNT(*) as total_stock_trades,
    COUNT(CASE WHEN fees = 0 THEN 1 END) as trades_with_zero_fees,
    SUM(profit_loss) as total_profit_loss
FROM trades 
WHERE trade_type = 'stock';
