import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

// Simple in-memory rate limiting (for production, use Redis or similar)
const rateLimitMap = new Map<string, { count: number; lastReset: number }>()

// Rate limiting configuration
const RATE_LIMITS = {
  '/api/auth': { requests: 5, windowMs: 15 * 60 * 1000 }, // 5 requests per 15 minutes for auth
  '/api/trades': { requests: 100, windowMs: 60 * 1000 }, // 100 requests per minute for trades
  default: { requests: 50, windowMs: 60 * 1000 } // 50 requests per minute for other endpoints
}

function getRateLimit(pathname: string) {
  for (const [path, limit] of Object.entries(RATE_LIMITS)) {
    if (path !== 'default' && pathname.startsWith(path)) {
      return limit
    }
  }
  return RATE_LIMITS.default
}

function getClientIP(request: NextRequest): string {
  // Try to get real IP from headers (for production behind proxy)
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  
  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }
  
  if (realIP) {
    return realIP
  }
  
  // Fallback IP
  return '127.0.0.1'
}

export function middleware(request: NextRequest) {
  // Only apply rate limiting to API routes
  if (!request.nextUrl.pathname.startsWith('/api/')) {
    return NextResponse.next()
  }

  const ip = getClientIP(request)
  const pathname = request.nextUrl.pathname
  const rateLimit = getRateLimit(pathname)
  
  const key = `${ip}:${pathname}`
  const now = Date.now()

  if (!rateLimitMap.has(key)) {
    rateLimitMap.set(key, { count: 0, lastReset: now })
  }

  const ipData = rateLimitMap.get(key)!
  
  // Reset counter if window has passed
  if (now - ipData.lastReset > rateLimit.windowMs) {
    ipData.count = 0
    ipData.lastReset = now
  }

  // Check if rate limit exceeded
  if (ipData.count >= rateLimit.requests) {
    return new NextResponse(
      JSON.stringify({
        error: 'Too Many Requests',
        message: 'Rate limit exceeded. Please try again later.',
        retryAfter: Math.ceil((ipData.lastReset + rateLimit.windowMs - now) / 1000)
      }),
      {
        status: 429,
        headers: {
          'Content-Type': 'application/json',
          'Retry-After': String(Math.ceil((ipData.lastReset + rateLimit.windowMs - now) / 1000)),
          'X-RateLimit-Limit': String(rateLimit.requests),
          'X-RateLimit-Remaining': String(Math.max(0, rateLimit.requests - ipData.count - 1)),
          'X-RateLimit-Reset': String(Math.ceil((ipData.lastReset + rateLimit.windowMs) / 1000))
        }
      }
    )
  }

  // Increment counter
  ipData.count += 1

  // Add rate limit headers to response
  const response = NextResponse.next()
  response.headers.set('X-RateLimit-Limit', String(rateLimit.requests))
  response.headers.set('X-RateLimit-Remaining', String(Math.max(0, rateLimit.requests - ipData.count)))
  response.headers.set('X-RateLimit-Reset', String(Math.ceil((ipData.lastReset + rateLimit.windowMs) / 1000)))

  return response
}

// Clean up old entries periodically (basic memory management)
setInterval(() => {
  const now = Date.now()
  const maxAge = 24 * 60 * 60 * 1000 // 24 hours
  
  for (const [key, data] of rateLimitMap.entries()) {
    if (now - data.lastReset > maxAge) {
      rateLimitMap.delete(key)
    }
  }
}, 60 * 60 * 1000) // Clean up every hour

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
}
