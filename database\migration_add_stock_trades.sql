-- Migration: Add Stock Trading Support to Existing Trades Table
-- Run this in your Supabase SQL Editor

-- Step 1: Add new columns for stock trading
ALTER TABLE trades 
ADD COLUMN IF NOT EXISTS trade_type VARCHAR(6) DEFAULT 'option' CHECK (trade_type IN ('option', 'stock')),
ADD COLUMN IF NOT EXISTS direction VARCHAR(5) CHECK (direction IN ('long', 'short')),
ADD COLUMN IF NOT EXISTS shares INTEGER,
ADD COLUMN IF NOT EXISTS stop_price DECIMAL(10,2),
ADD COLUMN IF NOT EXISTS r_return DECIMAL(8,4);

-- Step 2: Update existing records to have trade_type = 'option'
UPDATE trades SET trade_type = 'option' WHERE trade_type IS NULL;

-- Step 3: Make trade_type NOT NULL after setting defaults
ALTER TABLE trades ALTER COLUMN trade_type SET NOT NULL;

-- Step 4: Add constraints for data integrity (drop first if they exist)
DO $$
BEGIN
    -- Drop constraints if they exist
    BEGIN
        ALTER TABLE trades DROP CONSTRAINT IF EXISTS valid_option_trade;
    EXCEPTION
        WHEN undefined_object THEN NULL;
    END;

    BEGIN
        ALTER TABLE trades DROP CONSTRAINT IF EXISTS valid_stock_trade;
    EXCEPTION
        WHEN undefined_object THEN NULL;
    END;
END $$;

-- Add the constraints
ALTER TABLE trades
ADD CONSTRAINT valid_option_trade CHECK (
    trade_type != 'option' OR (
        option_type IS NOT NULL AND
        option_strike IS NOT NULL AND
        option_expiration IS NOT NULL AND
        contracts IS NOT NULL AND
        contracts > 0
    )
);

ALTER TABLE trades
ADD CONSTRAINT valid_stock_trade CHECK (
    trade_type != 'stock' OR (
        direction IS NOT NULL AND
        shares IS NOT NULL AND
        shares > 0
    )
);

-- Step 5: Drop the old trigger if it exists
DROP TRIGGER IF EXISTS calculate_profit_loss_trigger ON trades;

-- Step 6: Create updated function to handle both stock and option calculations
CREATE OR REPLACE FUNCTION calculate_profit_loss()
RETURNS TRIGGER AS $$
BEGIN
    -- Calculate fees based on trade type
    IF NEW.trade_type = 'option' THEN
        -- Options: $1.30 per contract
        NEW.fees = NEW.contracts * 1.30;
    ELSE
        -- Stocks: No fees
        NEW.fees = 0;
    END IF;

    -- Calculate profit/loss when both entry and exit prices are available
    IF NEW.exit_price IS NOT NULL AND NEW.entry_price IS NOT NULL THEN
        IF NEW.trade_type = 'option' THEN
            -- Options: multiply by 100 for contract multiplier
            NEW.profit_loss = (NEW.exit_price - NEW.entry_price) * NEW.contracts * 100 - NEW.fees;
        ELSE
            -- Stocks: calculate based on direction (no fees)
            IF NEW.direction = 'long' THEN
                NEW.profit_loss = (NEW.exit_price - NEW.entry_price) * NEW.shares;
            ELSE -- short
                NEW.profit_loss = (NEW.entry_price - NEW.exit_price) * NEW.shares;
            END IF;
        END IF;
    END IF;

    -- Calculate R-return for stock trades (risk-reward ratio)
    IF NEW.trade_type = 'stock' AND NEW.stop_price IS NOT NULL AND NEW.entry_price IS NOT NULL THEN
        DECLARE
            risk_amount DECIMAL(10,2);
            reward_amount DECIMAL(10,2);
        BEGIN
            IF NEW.direction = 'long' THEN
                -- Long: risk is entry - stop, reward is exit - entry
                risk_amount = (NEW.entry_price - NEW.stop_price) * NEW.shares;
                IF NEW.exit_price IS NOT NULL THEN
                    reward_amount = (NEW.exit_price - NEW.entry_price) * NEW.shares;
                    IF risk_amount > 0 THEN
                        NEW.r_return = reward_amount / risk_amount;
                    END IF;
                END IF;
            ELSE -- short
                -- Short: risk is stop - entry, reward is entry - exit
                risk_amount = (NEW.stop_price - NEW.entry_price) * NEW.shares;
                IF NEW.exit_price IS NOT NULL THEN
                    reward_amount = (NEW.entry_price - NEW.exit_price) * NEW.shares;
                    IF risk_amount > 0 THEN
                        NEW.r_return = reward_amount / risk_amount;
                    END IF;
                END IF;
            END IF;
        END;
    END IF;

    -- Update the updated_at timestamp
    NEW.updated_at = NOW();

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Step 7: Create the trigger
CREATE TRIGGER calculate_profit_loss_trigger
    BEFORE INSERT OR UPDATE ON trades
    FOR EACH ROW
    EXECUTE FUNCTION calculate_profit_loss();

-- Step 8: Enable Row Level Security (if not already enabled)
ALTER TABLE trades ENABLE ROW LEVEL SECURITY;

-- Step 9: Create RLS policies (drop and recreate to avoid conflicts)
DO $$
BEGIN
    -- Drop existing policies if they exist
    DROP POLICY IF EXISTS "Users can view their own trades" ON trades;
    DROP POLICY IF EXISTS "Users can insert their own trades" ON trades;
    DROP POLICY IF EXISTS "Users can update their own trades" ON trades;
    DROP POLICY IF EXISTS "Users can delete their own trades" ON trades;
EXCEPTION
    WHEN undefined_object THEN NULL;
END $$;

-- Create the policies
CREATE POLICY "Users can view their own trades" ON trades
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own trades" ON trades
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own trades" ON trades
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own trades" ON trades
    FOR DELETE USING (auth.uid() = user_id);
