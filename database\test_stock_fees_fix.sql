-- Test script to verify stock trades no longer have fees
-- Run this in your Supabase SQL Editor AFTER running the fix_stock_trade_fees.sql script

-- Test 1: Insert a new stock trade and verify fees are 0
INSERT INTO trades (
    user_id,
    entry_datetime,
    exit_datetime,
    ticker,
    trade_type,
    direction,
    shares,
    stop_price,
    entry_price,
    exit_price
) VALUES (
    auth.uid(), -- Replace with actual user ID if testing manually
    '2024-01-15 09:30:00+00',
    '2024-01-15 15:30:00+00',
    'AAPL',
    'stock',
    'long',
    100,
    150.00,
    155.00,
    160.00
);

-- Test 2: Verify the inserted trade has correct calculations
SELECT 
    ticker,
    trade_type,
    direction,
    shares,
    entry_price,
    exit_price,
    fees,
    profit_loss,
    -- Expected profit_loss should be: (160.00 - 155.00) * 100 = 500.00
    CASE 
        WHEN fees = 0 AND profit_loss = 500.00 THEN 'PASS'
        ELSE 'FAIL'
    END as test_result
FROM trades 
WHERE ticker = 'AAPL' AND trade_type = 'stock'
ORDER BY entry_datetime DESC
LIMIT 1;

-- Test 3: Insert a short stock trade
INSERT INTO trades (
    user_id,
    entry_datetime,
    exit_datetime,
    ticker,
    trade_type,
    direction,
    shares,
    stop_price,
    entry_price,
    exit_price
) VALUES (
    auth.uid(), -- Replace with actual user ID if testing manually
    '2024-01-16 09:30:00+00',
    '2024-01-16 15:30:00+00',
    'TSLA',
    'stock',
    'short',
    50,
    210.00,
    200.00,
    195.00
);

-- Test 4: Verify the short trade has correct calculations
SELECT 
    ticker,
    trade_type,
    direction,
    shares,
    entry_price,
    exit_price,
    fees,
    profit_loss,
    -- Expected profit_loss should be: (200.00 - 195.00) * 50 = 250.00
    CASE 
        WHEN fees = 0 AND profit_loss = 250.00 THEN 'PASS'
        ELSE 'FAIL'
    END as test_result
FROM trades 
WHERE ticker = 'TSLA' AND trade_type = 'stock'
ORDER BY entry_datetime DESC
LIMIT 1;

-- Test 5: Verify all existing stock trades have fees = 0
SELECT 
    'All Stock Trades Check' as test_name,
    COUNT(*) as total_stock_trades,
    COUNT(CASE WHEN fees = 0 THEN 1 END) as trades_with_zero_fees,
    CASE 
        WHEN COUNT(*) = COUNT(CASE WHEN fees = 0 THEN 1 END) THEN 'PASS'
        ELSE 'FAIL'
    END as test_result
FROM trades 
WHERE trade_type = 'stock';

-- Test 6: Verify option trades still have fees
SELECT 
    'Option Trades Check' as test_name,
    COUNT(*) as total_option_trades,
    COUNT(CASE WHEN fees > 0 THEN 1 END) as trades_with_fees,
    CASE 
        WHEN COUNT(*) = COUNT(CASE WHEN fees > 0 THEN 1 END) THEN 'PASS'
        ELSE 'FAIL'
    END as test_result
FROM trades 
WHERE trade_type = 'option' AND contracts IS NOT NULL;

-- Clean up test data (optional - comment out if you want to keep test trades)
DELETE FROM trades WHERE ticker IN ('AAPL', 'TSLA') AND trade_type = 'stock';
