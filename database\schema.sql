-- Trading Journal Database Schema
-- Run this in your Supabase SQL Editor

-- Create trades table
CREATE TABLE IF NOT EXISTS trades (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    entry_datetime TIMESTAMPTZ NOT NULL,
    exit_datetime TIMESTAMPTZ,
    ticker VARCHAR(10) NOT NULL,
    trade_type VARCHAR(6) NOT NULL DEFAULT 'option' CHECK (trade_type IN ('option', 'stock')),
    -- Option-specific fields
    option_type VARCHAR(4) CHECK (option_type IN ('Call', 'Put')),
    option_strike DECIMAL(10,2),
    option_expiration DATE,
    contracts INTEGER, -- For options
    -- Stock-specific fields
    direction VARCHAR(5) CHECK (direction IN ('long', 'short')),
    shares INTEGER, -- For stocks
    stop_price DECIMAL(10,2), -- Stop loss price for risk management
    risk_amount DECIMAL(10,2), -- Risk amount for stock trades
    mfe_price DECIMAL(10,2), -- Maximum Favorable Excursion price for stock trades
    -- Common fields
    entry_price DECIMAL(10,2) NOT NULL,
    exit_price DECIMAL(10,2),
    fees DECIMAL(10,2),
    profit_loss DECIMAL(10,2),
    percentage_return DECIMAL(8,4), -- Percentage return (e.g., 15.25 for 15.25%)
    r_return DECIMAL(8,4), -- R-return based on stop price and entry price
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    -- Constraints to ensure proper data based on trade type
    CONSTRAINT valid_option_trade CHECK (
        trade_type != 'option' OR (
            option_type IS NOT NULL AND
            option_strike IS NOT NULL AND
            option_expiration IS NOT NULL AND
            contracts IS NOT NULL AND
            contracts > 0
        )
    ),
    CONSTRAINT valid_stock_trade CHECK (
        trade_type != 'stock' OR (
            direction IS NOT NULL AND
            shares IS NOT NULL AND
            shares > 0
        )
    )
);

-- Create function to automatically calculate fees, profit/loss, and R-return
CREATE OR REPLACE FUNCTION calculate_profit_loss()
RETURNS TRIGGER AS $$
BEGIN
    -- Calculate fees based on trade type
    IF NEW.trade_type = 'option' THEN
        -- Options: $1.30 per contract
        NEW.fees = NEW.contracts * 1.30;
    ELSE
        -- Stocks: No fees
        NEW.fees = 0;
    END IF;

    -- Calculate profit/loss when both entry and exit prices are available
    IF NEW.exit_price IS NOT NULL AND NEW.entry_price IS NOT NULL THEN
        IF NEW.trade_type = 'option' THEN
            -- Options: multiply by 100 for contract multiplier
            NEW.profit_loss = (NEW.exit_price - NEW.entry_price) * NEW.contracts * 100 - NEW.fees;
        ELSE
            -- Stocks: calculate based on direction (no fees)
            IF NEW.direction = 'long' THEN
                NEW.profit_loss = (NEW.exit_price - NEW.entry_price) * NEW.shares;
            ELSE -- short
                NEW.profit_loss = (NEW.entry_price - NEW.exit_price) * NEW.shares;
            END IF;
        END IF;
    END IF;

    -- Calculate R-return for stock trades (risk-reward ratio)
    IF NEW.trade_type = 'stock' AND NEW.stop_price IS NOT NULL AND NEW.entry_price IS NOT NULL THEN
        DECLARE
            risk_amount DECIMAL(10,2);
            reward_amount DECIMAL(10,2);
        BEGIN
            IF NEW.direction = 'long' THEN
                -- Long: risk is entry - stop, reward is exit - entry
                risk_amount = (NEW.entry_price - NEW.stop_price) * NEW.shares;
                IF NEW.exit_price IS NOT NULL THEN
                    reward_amount = (NEW.exit_price - NEW.entry_price) * NEW.shares;
                    IF risk_amount > 0 THEN
                        NEW.r_return = reward_amount / risk_amount;
                    END IF;
                END IF;
            ELSE -- short
                -- Short: risk is stop - entry, reward is entry - exit
                risk_amount = (NEW.stop_price - NEW.entry_price) * NEW.shares;
                IF NEW.exit_price IS NOT NULL THEN
                    reward_amount = (NEW.entry_price - NEW.exit_price) * NEW.shares;
                    IF risk_amount > 0 THEN
                        NEW.r_return = reward_amount / risk_amount;
                    END IF;
                END IF;
            END IF;
        END;
    END IF;

    -- Update the updated_at timestamp
    NEW.updated_at = NOW();

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically calculate profit/loss
DROP TRIGGER IF EXISTS calculate_profit_loss_trigger ON trades;
CREATE TRIGGER calculate_profit_loss_trigger
    BEFORE INSERT OR UPDATE ON trades
    FOR EACH ROW
    EXECUTE FUNCTION calculate_profit_loss();

-- Create index for faster queries
CREATE INDEX IF NOT EXISTS idx_trades_entry_datetime ON trades(entry_datetime);
CREATE INDEX IF NOT EXISTS idx_trades_ticker ON trades(ticker);
CREATE INDEX IF NOT EXISTS idx_trades_created_at ON trades(created_at);

-- Create view for daily statistics
CREATE OR REPLACE VIEW daily_stats AS
SELECT 
    DATE(entry_datetime) as trade_date,
    COUNT(*) as trade_count,
    SUM(CASE WHEN profit_loss > 0 THEN 1 ELSE 0 END) as winning_trades,
    SUM(CASE WHEN profit_loss < 0 THEN 1 ELSE 0 END) as losing_trades,
    COALESCE(SUM(profit_loss), 0) as total_profit_loss,
    COALESCE(AVG(profit_loss), 0) as avg_profit_loss
FROM trades 
WHERE exit_price IS NOT NULL
GROUP BY DATE(entry_datetime)
ORDER BY trade_date DESC;

-- Enable Row Level Security (RLS)
ALTER TABLE trades ENABLE ROW LEVEL SECURITY;

-- Create policies for user-specific access
CREATE POLICY "Users can view their own trades" ON trades
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own trades" ON trades
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own trades" ON trades
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own trades" ON trades
    FOR DELETE USING (auth.uid() = user_id);

-- Create user preferences table
CREATE TABLE IF NOT EXISTS user_preferences (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL UNIQUE,
    theme VARCHAR(10) CHECK (theme IN ('light', 'dark')) DEFAULT 'light',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable Row Level Security (RLS) for user_preferences
ALTER TABLE user_preferences ENABLE ROW LEVEL SECURITY;

-- Create policies for user-specific access to preferences
CREATE POLICY "Users can view their own preferences" ON user_preferences
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own preferences" ON user_preferences
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own preferences" ON user_preferences
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own preferences" ON user_preferences
    FOR DELETE USING (auth.uid() = user_id);

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_user_preferences_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update updated_at timestamp
DROP TRIGGER IF EXISTS update_user_preferences_updated_at_trigger ON user_preferences;
CREATE TRIGGER update_user_preferences_updated_at_trigger
    BEFORE UPDATE ON user_preferences
    FOR EACH ROW
    EXECUTE FUNCTION update_user_preferences_updated_at();

-- Note: Sample data removed due to user authentication requirements
-- Users will need to add their own trades after signing up
