# Day Trading Journal

A modern, responsive web application for tracking day trading activities with calendar view, trade logging, and performance analytics.

## Features

- **User Authentication**: Secure login/signup system with Supa<PERSON> Auth
- **Personal Data**: Each user sees only their own trades and data
- **Calendar View**: Visual calendar showing daily P&L and trade counts with monthly summary stats
- **Trade Entry**: Comprehensive form for logging trades with all essential fields
- **Trade Log**: Searchable table with real-time statistics, advanced filtering, and CSV export
- **Real-time Calculations**: Automatic profit/loss and percentage return calculations
- **Dark Mode**: Toggle between light and dark themes with persistent preference
- **Responsive Design**: Works seamlessly on desktop and mobile devices

## Tech Stack

- **Frontend**: Next.js 15 with TypeScript
- **Styling**: Tailwind CSS
- **Database**: Supabase (PostgreSQL)
- **Forms**: React Hook Form with Zod validation
- **Icons**: Lucide React
- **Date Handling**: date-fns

## Setup Instructions

### 1. Install Dependencies

```bash
npm install
```

### 2. Set up Supabase

1. Go to [Supabase](https://supabase.com) and create a new project
2. Copy your project URL and anon key
3. Update `.env.local` with your Supabase credentials:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

4. **Important**: Enable email authentication in your Supabase project:
   - Go to Authentication → Settings in your Supabase dashboard
   - Ensure "Enable email confirmations" is configured as needed
   - Configure email templates if desired

### 3. Set up Database

1. Go to your Supabase project dashboard
2. Navigate to the SQL Editor
3. Copy and paste the contents of `database/schema.sql`
4. Run the SQL to create the necessary tables and functions

### 4. Run the Application

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to view the application.

## Usage

### Getting Started

1. **Sign Up/Sign In**: Create an account or sign in to access your personal trading journal
2. **Demo Mode**: If Supabase is not configured, the app runs in demo mode with sample data

### Adding Trades

1. **Authentication Required**: You must be signed in to add trades (button hidden for non-authenticated users)
2. Click the "Add Trade" button on the calendar view
3. Fill in the trade details:
   - **Entry Date/Time**: When you entered the trade
   - **Exit Date/Time**: When you exited (optional for open positions)
   - **Ticker**: Stock/option symbol
   - **Option Type**: Call or Put (for options trading)
   - **Option Strike**: Strike price for options
   - **Option Expiration**: Expiration date for options
   - **Contracts**: Number of contracts/shares
   - **Total Fees**: Automatically calculated at $1.30 per contract
   - **Entry Price**: Price at entry
   - **Exit Price**: Price at exit (optional for open positions)

### Managing Trades

- **Add Trades**: Click "Add Trade" button to create new trades
- **Edit Trades**: Click the edit (pencil) icon in the trade log to modify existing trades
- **Delete Trades**: Click the delete (trash) icon to remove trades
- **Search Trades**: Use the search box to find trades by ticker symbol

### Viewing Performance

- **Calendar View**: See daily P&L and trade counts at a glance
- **Monthly Summary**: View total P&L, trade count, trading days, and win rate for the current month
- **Month/Year Navigation**: Click on month/year to quickly jump to any month (2010-current year)
- **Trade Log**: View detailed list with comprehensive statistics cards
- **Real-time Stats**: Total P&L, win rate, average return, best/worst trades, and more
- **Advanced Filtering**: Date range, win/loss, option type, day of week, contract size filters
- **CSV Export**: Download filtered trades with all data for external analysis
- **Dynamic Updates**: Statistics update automatically with search and filters
- Click on any calendar date to filter trades for that specific day

## Key Features

### User Authentication & Data Security
- **Secure Authentication**: Email/password authentication via Supabase Auth
- **Personal Data Isolation**: Each user can only see and manage their own trades
- **Row-Level Security**: Database-level security ensures data privacy
- **Session Management**: Automatic session handling and secure logout
- **Demo Mode**: Works without authentication when Supabase is not configured

### Automatic Calculations
- **Fee Calculation**: $1.30 per contract automatically deducted from P&L
- **Profit/Loss**: (Exit Price - Entry Price) × Contracts × 100 - Fees
- **Percentage Return**: (P&L / ((Entry Price × 100) × Contracts)) × 100
- **Real-time Updates**: All calculations update automatically when trade details change
- **Options Multiplier**: 100x multiplier accounts for options contracts representing 100 shares each

### Option Type Support
- Dropdown selection for Call or Put options
- Option type indicators ("C" for Call, "P" for Put) displayed next to strike prices
- Compact display in trade log for easy identification
- Supports both stock and options trading

### Enhanced Trade Management
- **Add Trades**: Comprehensive form with validation
- **Edit Trades**: Modify any trade details with automatic recalculation
- **Delete Trades**: Remove trades with confirmation
- **Real-time Updates**: Calendar and statistics update immediately

### Database Schema

The application uses the following main table structure:

```sql
trades (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  entry_datetime TIMESTAMPTZ NOT NULL,
  exit_datetime TIMESTAMPTZ,
  ticker VARCHAR(10) NOT NULL,
  option_type VARCHAR(4) CHECK (option_type IN ('Call', 'Put')),
  option_strike DECIMAL(10,2),
  option_expiration DATE,
  contracts INTEGER NOT NULL DEFAULT 1,
  entry_price DECIMAL(10,2) NOT NULL,
  exit_price DECIMAL(10,2),
  fees DECIMAL(10,2), -- Automatically calculated
  profit_loss DECIMAL(10,2), -- Automatically calculated: (exit-entry) * contracts * 100 - fees
  percentage_return DECIMAL(8,4), -- Automatically calculated: (P&L / ((entry_price * 100) * contracts)) * 100
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
)

-- Row Level Security (RLS) Policies:
-- Users can only view, insert, update, and delete their own trades
```
